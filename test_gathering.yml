---
# Test playbook to demonstrate different gathering behaviors
# Usage: ansible-playbook test_gathering.yml

- name: Test gathering behavior - No facts needed
  hosts: localhost
  connection: local
  gather_facts: false # Explicit control regardless of gathering setting
  tasks:
    - name: Simple task that doesn't need facts
      ansible.builtin.debug:
        msg: "This task doesn't need system information"

    - name: Ping test
      ansible.builtin.ping:

- name: Test gathering behavior - Facts needed
  hosts: localhost
  connection: local
  # gather_facts setting depends on ansible.cfg gathering value
  tasks:
    - name: Task that needs facts (will trigger collection with smart/implicit)
      ansible.builtin.debug:
        msg: |
          Operating System: {{ ansible_distribution | default('Unknown') }}
          OS Version: {{ ansible_distribution_version | default('Unknown') }}
          Architecture: {{ ansible_architecture | default('Unknown') }}
          Python Version: {{ ansible_python_version | default('Unknown') }}
          Hostname: {{ ansible_hostname | default('Unknown') }}

    - name: Check if facts were collected
      ansible.builtin.debug:
        msg: "Facts collection status: {{ 'Collected' if ansible_facts is defined else 'Not collected' }}"

- name: Test gathering behavior - Conditional facts usage
  hosts: localhost
  connection: local
  tasks:
    - name: Use facts only if available (smart gathering benefit)
      ansible.builtin.debug:
        msg: "Memory info: {{ ansible_memtotal_mb | default('Not available') }} MB"
      when: ansible_memtotal_mb is defined

    - name: Task that always runs regardless of facts
      ansible.builtin.debug:
        msg: "This task runs with any gathering setting"

- name: Performance test - Multiple hosts simulation
  hosts: localhost
  connection: local
  tasks:
    - name: Simulate time-sensitive task
      ansible.builtin.debug:
        msg: "Task executed at {{ ansible_date_time.iso8601 | default('Time not available') }}"

    - name: Report gathering impact
      ansible.builtin.debug:
        msg: |
          Gathering setting impact:
          - implicit: Always collects facts (slower but comprehensive)
          - explicit: Only collects when gather_facts: yes (fastest for simple tasks)
          - smart: Collects only when facts are used (balanced approach)
