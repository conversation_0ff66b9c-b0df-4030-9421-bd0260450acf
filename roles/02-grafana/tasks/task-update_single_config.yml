---
# This task file is included by a loop. It expects 'config_item' as the loop variable.
# It handles the logic for updating a single configuration file and restarting the associated service.

- name: "Update Config | Copy {{ config_item.config_file }} to {{ MEDICAL.monitor.remote_root_path }}/{{ config_item.relative_dir }}/{{ config_item.config_file }}"
  ansible.builtin.copy:
    src: "{{ config_item.config_file }}"
    dest: "{{ MEDICAL.monitor.remote_root_path }}/{{ config_item.relative_dir }}/{{ config_item.config_file }}"
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: "0644"
    backup: true
  register: copy_result
  become: false

- name: "Update Config | Restart {{ config_item.service_to_restart }} to apply changes"
  ansible.builtin.command: "docker-compose up -d --force-recreate {{ config_item.service_to_restart }}"
  args:
    chdir: "{{ MEDICAL.monitor.remote_root_path }}"
  when: copy_result.changed
  become: false
  changed_when: copy_result.changed
