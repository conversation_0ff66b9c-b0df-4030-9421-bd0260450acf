---
# Task Description: This file handles the upgrade process for monitoring services,
# including syncing configuration files and updating Docker containers based on version definitions.

# 1. Pre-task: Sync docker-compose.yml file
#    Checks the 'sync_docker_compose' variable from monitor.yml to determine if execution is needed.
#    If true, it syncs 'files/docker-compose.yml' to the deployment directory on the remote host.
- name: "Pre-task: Sync docker-compose.yml"
  ansible.builtin.copy:
    src: docker-compose.yml
    dest: "{{ MEDICAL.monitor.remote_root_path }}/docker-compose.yml"
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: "0644"
    backup: true
  when: sync_docker_compose | default(false) | bool
  become: false

# 2. Core task: Generate and sync .env configuration file
#    Uses the 'templates/env.j2' template to create the '.env' file, which contains service versions and other info.
#    This file provides environment variables for subsequent docker-compose commands.
- name: "Core task: Generate and sync .env configuration file"
  ansible.builtin.template:
    src: env.j2
    dest: "{{ MEDICAL.monitor.remote_root_path }}/.env"
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
    mode: "0644"
    backup: true
  register: env_file_result
  become: false

# 3. Core task: Loop through services and include upgrade logic for each.
#    This approach avoids the "loop on a block" error by looping over an `include_tasks` action.
#    The entire loop is skipped if the .env file did not change.
- name: "Core task: Include and loop service upgrade tasks"
  ansible.builtin.include_tasks: task-upgrade_single_service.yml
  loop:
    - grafana
    - loki
    - promtail
    - prometheus
    - node-exporter
  loop_control:
    loop_var: service_name
  when: env_file_result.changed
