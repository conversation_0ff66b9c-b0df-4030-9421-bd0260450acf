# Ansible Auto-Ops Platform

This repository contains an Ansible-based automation platform for infrastructure management and performance testing. The platform supports multiple project environments including medical backend operations and performance testing scenarios.

## Features

- **Multi-Project Architecture**: Organized structure for different operational domains
- **Infrastructure Automation**: Complete server setup and configuration management
- **Performance Testing Framework**: Integrated Locust-based load testing capabilities
- **Security Integration**: Vault encryption support for sensitive data
- **Git Hooks Integration**: Automated validation and quality checks
- **Container Integration**: Docker-based deployment support with Semaphore UI

## Platform Architecture

```
ansible/
├── .githooks/                               # Git hooks for automation
├── env/                                     # Environment configurations
├── projects/                               # Individual project modules
│   ├── medical-backend/                    # Medical backend operations
│   │   ├── roles/                          # Medical-specific roles
│   │   ├── inventory/                      # Medical infrastructure inventory
│   │   ├── site.yml                       # Medical deployment playbook
│   │   └── README.md                      # Medical backend documentation
│   └── performance/                       # Performance testing framework
│       ├── roles/                          # Performance testing roles
│       ├── extra-vars/                     # Test scenario configurations
│       ├── site.yml                       # Performance testing playbook
│       └── README.md                      # Performance testing documentation
├── vault-vars.sh                          # Vault encryption utility
└── README.md                              # This documentation
```

## Prerequisites

### System Requirements

- Ubuntu/Debian-based Linux system
- Python 3.6 or higher
- Git with proper configuration
- Docker and Docker Compose (for Semaphore UI)
- SSH access to target servers

### Required Tools

1. **Ansible**: Infrastructure automation engine
2. **Semaphore**: Web-based Ansible UI (optional but recommended)
3. **Git**: Version control with hooks integration
4. **Docker**: Container runtime for Semaphore deployment

## Installation

### Installing Ansible

```bash
# Add Ansible repository
sudo apt-add-repository ppa:ansible/ansible
sudo apt update

# Install Ansible
sudo apt install ansible

# Upgrade Ansible (when needed)
sudo apt update
sudo apt upgrade ansible

# Verify installation
ansible --version
```

### Installing Semaphore UI

Semaphore provides a web-based interface for managing Ansible operations. While the platform supports full Docker deployment, recommend a hybrid approach for better performance and resource management.

#### Recommended Installation Method (Hybrid)

For optimal performance, recommend using containers only for the database layer and running Semaphore as a binary:

1. **Use Docker Compose for database only**: Deploy PostgreSQL database using the provided docker-compose configuration
2. **Run Semaphore as binary**: Use the native Semaphore binary for the web interface and execution engine

**Benefits of the hybrid approach:**
- Better performance and resource utilization
- Easier maintenance and updates
- More flexible configuration options
- Reduced container overhead

👉 **[Binary Installation Guide](env/Binary.md)** - Complete setup instructions for binary deployment

#### Full Docker Deployment (Alternative)

If you prefer a fully containerized setup, you can use the complete Docker Compose configuration:

```bash
# Create custom docker network
sudo docker network create ${YOUR_NETWORK_NAME}

# Verify configuration
sudo docker-compose config
# Or with environment file
sudo docker-compose --env-file .env config

# Start Semaphore (full stack)
sudo docker-compose up -d
```

Configuration details are available in: [docker-compose.yml](env/docker-compose.yml)

#### Database-Only Docker Deployment (Recommended)

For the hybrid approach, start only the database service:

```bash
# Create custom docker network
sudo docker network create ${YOUR_NETWORK_NAME}

# Start only PostgreSQL database
sudo docker-compose up -d semaphore_db

# Verify database is running
sudo docker-compose ps semaphore_db
```

Then follow the [Binary Installation Guide](env/Binary.md) to set up Semaphore binary with the containerized database.

## Configuration

### Git Configuration Setup

Ensure proper Git configuration for hooks and file permissions:

```bash
# Check and set git filemode
git config --get core.filemode
# If not true, set it
git config core.filemode true

# Check and set git hooks path
git config --get core.hooksPath
# If not .githooks, set it
git config core.hooksPath .githooks
```

### Vault Configuration

For projects using encrypted variables, configure vault password files as needed. The vault encryption utility `vault-vars.sh` can be used to encrypt sensitive configuration files.

## Usage

### General Command Structure

> 📌 **Important Note:**
> The `--vault-password-file VPF` parameter in the following commands is only required when `vault-vars.sh` has been used to encrypt `projects/<project_dir_name>/roles/*/vars/main.yml` files. Otherwise, this parameter can be omitted!

### Discovery Commands

Use these commands to explore and understand the available resources:

```bash
# List all target hosts for a project
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-hosts

# List all available tags
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-tags

# List all tasks that would be executed
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-tasks
```

### Execution Commands

Execute playbooks with full or selective task execution:

```bash
# Execute all tasks in a project
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF

# Execute specific tasks using tags
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -t <tag-name>

# Execute with extra variables
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -e "variable=value"

# Execute with verbose output for debugging
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -vvv
```

## Available Projects

### Medical Backend Operations

Complete infrastructure automation for medical backend systems including:
- Server provisioning and configuration
- Security hardening and compliance
- Application deployment automation
- Health monitoring and maintenance scripts

👉 **[Medical Backend Documentation](projects/medical-backend/README.md)**

#### Quick Start Examples

```bash
# Execute health check scripts on web servers
ansible-playbook projects/medical-backend/site.yml -e "script_name=health_check.sh target_group=web_servers"

# Deploy backend services
ansible-playbook projects/medical-backend/site.yml -t deploy_backend

# Run maintenance scripts
ansible-playbook projects/medical-backend/site.yml -e "script_name=maintenance.sh target_group=all"
```

### Performance Testing Framework

Comprehensive load testing infrastructure using Locust:
- Automated test environment deployment
- Distributed load testing capabilities
- Performance metrics collection and analysis
- Multiple testing scenario support

👉 **[Performance Testing Documentation](projects/performance/README.md)**

#### Quick Start Examples

```bash
# Deploy complete performance testing environment
ansible-playbook projects/performance/site.yml -t deploy_all

# Run load test with enquete scenario
ansible-playbook projects/performance/site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"

# Prepare infrastructure only
ansible-playbook projects/performance/site.yml -t play_prepare
```

## Project Development Guidelines

### Adding New Projects

When creating new projects:

1. **Follow the established directory structure**
2. **Create comprehensive documentation** in project README
3. **Implement proper role dependencies**
4. **Include example configurations** and usage scenarios
5. **Test thoroughly** in development environments

### Role Development Standards

- Use descriptive role names with numbered prefixes (e.g., `01-prepare`, `02-deploy`)
- Implement idempotent operations
- Include proper error handling and validation
- Document all variables and their purposes
- Follow Ansible best practices for security

### Variable Management

- Use consistent naming conventions across projects
- Leverage vault encryption for sensitive data
- Organize variables by scope (global, role-specific, environment-specific)
- Provide default values where appropriate

## Security Best Practices

### Access Control

- Use SSH keys for authentication instead of passwords
- Implement least-privilege access principles
- Regularly rotate SSH keys and vault passwords
- Monitor and log all automation activities

### Data Protection

- Encrypt sensitive variables using Ansible Vault
- Avoid storing credentials in plain text
- Use secure networks for automation traffic
- Implement proper backup and recovery procedures

### Network Security

- Use VPNs or private networks for management traffic
- Implement firewall rules for Ansible communications
- Monitor network traffic for anomalies
- Secure Semaphore UI with proper authentication

## Monitoring and Logging

### Execution Logging

- Ansible execution logs are stored in project directories
- Semaphore provides web-based execution history
- Use verbose modes for detailed troubleshooting
- Monitor system resources during large deployments

### Health Monitoring

- Implement regular health checks using automated scripts
- Monitor target system performance and availability
- Set up alerting for critical infrastructure components
- Maintain deployment and change logs

## Troubleshooting

### Common Issues

1. **SSH Connection Problems**
   - Verify SSH keys and permissions
   - Check network connectivity to target hosts
   - Validate inventory configuration

2. **Vault Decryption Errors**
   - Ensure correct vault password file
   - Verify vault file encryption status
   - Check file permissions on vault files

3. **Role Dependency Issues**
   - Verify role paths and dependencies
   - Check for circular dependencies
   - Validate role metadata configuration

4. **Performance Issues**
   - Monitor system resources during execution
   - Optimize playbook parallelization settings
   - Review network latency to target hosts

### Debug Mode

Enable verbose output for detailed troubleshooting:

```bash
# Basic verbose output
ansible-playbook projects/<project>/site.yml -v

# Detailed debug information
ansible-playbook projects/<project>/site.yml -vvv

# Connection debugging
ansible-playbook projects/<project>/site.yml -vvvv
```

### Validation Commands

```bash
# Test connectivity to all hosts
ansible all -m ping -i projects/<project>/inventory/

# Check Ansible configuration
ansible-config dump

# Validate playbook syntax
ansible-playbook projects/<project>/site.yml --syntax-check

# Perform dry run without making changes
ansible-playbook projects/<project>/site.yml --check
```

## Contributing

### Development Workflow

1. **Create feature branches** for new development
2. **Follow coding standards** and documentation requirements
3. **Test thoroughly** in development environments
4. **Update documentation** for any changes
5. **Submit pull requests** with clear descriptions

### Code Quality

- Use Git hooks for automated validation
- Follow Ansible best practices and conventions
- Implement comprehensive error handling
- Include appropriate comments and documentation
- Test with different operating system versions

### Documentation Standards

- Keep README files updated with any changes
- Document all variables and their purposes
- Provide clear usage examples
- Include troubleshooting information
- Maintain version compatibility notes

## Support and Resources

### Internal Resources

- Project-specific documentation in individual README files
- Git hooks for automated validation and quality checks
- Semaphore UI for web-based management and monitoring

### External Resources

- [Ansible Documentation](https://docs.ansible.com/)
- [Ansible Best Practices](https://docs.ansible.com/ansible/latest/user_guide/playbooks_best_practices.html)
- [Semaphore Documentation](https://docs.semui.co/)
- [Docker Documentation](https://docs.docker.com/)

### Getting Help

1. **Check project-specific documentation** first
2. **Review troubleshooting sections** for common issues
3. **Use debug modes** for detailed error information
4. **Validate configurations** using provided commands
5. **Monitor system logs** for infrastructure-level issues