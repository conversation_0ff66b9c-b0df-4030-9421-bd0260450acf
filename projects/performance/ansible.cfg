# Ansible Configuration for Performance Testing Project
# This file contains project-specific settings optimized for performance testing infrastructure

[defaults]
# Set default inventory file path to local inventory directory
# Points to the inventory files containing target host information
inventory=./inventory

# Set default roles path to local roles directory
# Defines where Ansible looks for role definitions and dependencies
roles_path=./roles

# Set default module to 'shell' for ad-hoc commands
# Useful for performance testing scenarios that often require shell commands
module_name=shell

# Disable SSH host key checking to avoid interactive prompts during automation
# Security Note: Only use in trusted environments or with proper SSH key management
host_key_checking=False

# Disable password prompts for SSH connections
# Assumes SSH key-based authentication is properly configured
ask_pass=False

# Set connection timeout to 30 seconds for faster failure detection
# Optimized for performance testing where quick feedback is important
timeout = 30

# Optimize fact gathering for performance - only gather when explicitly needed
# Reduces overhead for performance testing scenarios where system facts aren't always required
gathering = smart

# Increase parallel processes to 20 for better performance testing deployment speed
# Higher concurrency suitable for performance testing infrastructure
forks = 20

# Specify log file location for Ansible execution logs
# Captures all playbook runs, task outputs, and error messages for debugging
log_path = ./ansible.log

# Disable task debugger to prevent interactive debugging sessions
# Ensures automated execution without human intervention in CI/CD pipelines
enable_task_debugger=False

# Disable deprecation warnings to reduce log noise
# Helps focus on actual errors and important messages during performance testing
deprecation_warnings=False

# Enable fact caching to improve performance on repeated runs
# Stores gathered facts to avoid re-collection on subsequent playbook runs
fact_caching = jsonfile
fact_caching_connection = /tmp/ansible_fact_cache
fact_caching_timeout = 3600

# Add performance monitoring callback plugins
# Provides timing information and performance metrics for deployment optimization
callback_whitelist = timer, profile_tasks

# Set maximum diff size for better debugging of large configuration changes
max_diff_size = 104857600

# Optimize for performance testing scenarios
retry_files_enabled = True
retry_files_save_path = ./retries

[ssh_connection]
# Enhanced SSH connection optimization arguments for better performance
# -C: Enable compression to reduce network bandwidth
# -o ControlMaster=auto: Enable SSH connection multiplexing for faster subsequent connections
# -o ControlPersist=300s: Keep master connection alive for 5 minutes (longer for performance tests)
# -o ServerAliveInterval=60: Send keepalive every 60 seconds
# -o ServerAliveCountMax=3: Maximum 3 failed keepalives before disconnect
# -o UserKnownHostsFile=/dev/null: Don't save host keys (for dynamic testing environments)
ssh_args = -C -o ControlMaster=auto -o ControlPersist=300s -o ServerAliveInterval=60 -o ServerAliveCountMax=3 -o UserKnownHostsFile=/dev/null

# Enable SSH pipelining for faster task execution
# Reduces SSH overhead by sending multiple commands through single connection
# Note: Requires 'requiretty' to be disabled in target system's sudoers file
pipelining = True

# Set SSH control path to avoid permission issues in multi-user environments
# Uses /tmp directory with process ID to ensure unique control sockets
control_path = /tmp/ansible-ssh-%%h-%%p-%%r-$$

# Connection retry settings for better reliability in performance testing environments
retries = 3

[privilege_escalation]
# Disable automatic privilege escalation (sudo) by default
# Performance testing usually doesn't require root privileges for basic operations
# Individual tasks can override this setting when needed
become=False

[inventory]
# Enable inventory plugins for dynamic inventory scenarios
enable_plugins = host_list, script, auto, yaml, ini

[colors]
# Enable colored output for better visibility during performance testing
highlight = white
verbose = blue
warn = bright purple
error = red
debug = dark gray
deprecate = purple
skip = cyan
unreachable = red
ok = green
changed = yellow
