---
- name: Check if docker is installed
  shell: docker version
  register: docker_installed
  ignore_errors: true
  changed_when: false

- name: Get os type
  shell: cat /etc/os-release | grep ^ID= | cut -d = -f 2
  register: os_type
  failed_when: os_type.stdout is none

- name: Install required dependencies
  apt:
    name:
      - apt-transport-https
      - ca-certificates
      - curl
      - software-properties-common
    state: present

- name: Add Docker's official GPG key
  block:
    - name: Add Docker GPG key
      get_url:
        url: "https://download.docker.com/linux/{{ os_type.stdout|lower }}/gpg"
        dest: /usr/share/keyrings/docker-archive-keyring.gpg.asc
        mode: "0644"
        force: true
      register: gpg_key
      retries: 5
      delay: 5
      until: gpg_key is success

    - name: Convert GPG key to binary format
      shell:
        cmd: gpg --dearmor < /usr/share/keyrings/docker-archive-keyring.gpg.asc | tee /usr/share/keyrings/docker-archive-keyring.gpg > /dev/null
        creates: /usr/share/keyrings/docker-archive-keyring.gpg
      when: gpg_key.changed

    - name: Remove temporary ASCII GPG key file
      file:
        path: /usr/share/keyrings/docker-archive-keyring.gpg.asc
        state: absent
      when: gpg_key.changed

    - name: Manually gather facts
      setup:
        filter: ansible_architecture,ansible_lsb

    - name: Add Docker APT repository
      lineinfile:
        path: /etc/apt/sources.list.d/docker.list
        line: "deb [arch={{ ansible_architecture }} signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/{{ os_type.stdout|lower }} {{ ansible_lsb.codename }} stable"
        state: present
        mode: "0644"
        create: true
      register: repo_added

    - name: Update apt cache after adding repository
      apt:
        update_cache: true
      when: repo_added.changed

    - name: Install Docker packages
      apt:
        name:
          - docker-ce
          - docker-ce-cli
          - containerd.io
          - docker-buildx-plugin
          - docker-compose-plugin
        state: present
  when: docker_installed.rc != 0

- name: Ensure Docker service is started and enabled
  systemd:
    name: docker
    state: started
    enabled: true

- name: Add user to docker group (optional, for non-root access)
  user:
    name: "{{ ansible_user | default('ubuntu') }}"
    groups: docker
    append: true
  register: user_group
  when: ansible_user != 'root'

- name: Test Docker installation
  ansible.builtin.command:
    cmd: docker run --rm hello-world
  register: docker_test
  changed_when: false
  failed_when: docker_test.rc != 0
  when: user_group is success or ansible_user is not defined

- name: Check if docker-compose is installed
  shell: docker-compose version
  register: docker_compose_installed
  ignore_errors: true
  changed_when: false

# - name: Install docker-compose
#   shell: |
#     curl -L "https://github.com/docker/compose/releases/download/v{{ PERF.dc_version }}/docker-compose-$(uname -s | tr 'A-Z' 'a-z')-$(uname -m | tr 'A-Z' 'a-z')" -o /usr/local/bin/docker-compose
#     chmod +x /usr/local/bin/docker-compose
#   register: output
#   when: docker_compose_installed.rc != 0

- name: Install docker-compose
  get_url:
    # url: "https://github.com/docker/compose/releases/download/v{{ PERF.dc_version }}/docker-compose-{{ ansible_system | lower }}-{{ ansible_architecture | lower }}"
    url: "https://github.com/docker/compose/releases/download/v{{ PERF.dc_version }}/docker-compose-$(uname -s | tr 'A-Z' 'a-z')-$(uname -m | tr 'A-Z' 'a-z')"
    dest: /usr/local/bin/docker-compose
    mode: "0755"
  register: output
  when: docker_compose_installed.rc != 0

- name: Assert docker-compose download successful
  assert:
    that: output.dest is defined
    fail_msg: "Failed to download docker-compose"
  when: docker_compose_installed.rc != 0
