---
- name: Check if known_hosts file exists on localhost
  stat:
    path: "{{ PERF.repo.ansible.semaphore_home_path }}/.ssh/known_hosts"
  register: known_hosts_stat
  delegate_to: localhost

- name: Create known_hosts file if it does not exist on localhost
  file:
    path: "{{ PERF.repo.ansible.semaphore_home_path }}/.ssh/known_hosts"
    state: touch
    mode: '0600'
  when: not known_hosts_stat.stat.exists
  delegate_to: localhost

- name: Check if host key exists in known_hosts
  shell: "ssh-keygen -F {{ item }}"
  register: host_key_check
  failed_when: false
  changed_when: false
  loop: "{{ groups['locust'] }}"
  delegate_to: localhost

- name: Add host key to known_hosts if not present
  known_hosts:
    name: "{{ item.item }}"
    key: "{{ lookup('pipe', 'ssh-keyscan -H {{ item.item }}') }}"
    path: "{{ PERF.repo.ansible.semaphore_home_path }}/.ssh/known_hosts"
    state: present
  when: item.rc != 0
  loop: "{{ host_key_check.results }}"
  delegate_to: localhost