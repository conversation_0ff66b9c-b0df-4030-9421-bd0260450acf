---
perf:
  home_path: "{{ ansible_env.HOME }}"
  dc_version: 2.33.0
  repo:
    ansible:
      ## For Local Machine with Binary
      semaphore_home_path: "{{ ansible_env.HOME }}"
      # The same path as tmp_path which in config.json
      repo_root_dir: docker-compose/semaphore/tmp

      ## For Docker Container (Not recommended)
      # semaphore_home_path: /home/<USER>
      # repo_root_dir: /tmp/semaphore

      ## For Local Machine with CLI
      # semaphore_home_path: "{{ ansible_env.HOME }}"
      # repo_root_dir: bitbucket

      repo_name: ansible
    perf:
      ssh_key_name: neox-bitbucket-testing
      address: *****************:neoxinc/testing.git
      repo_root_dir: bitbucket
      repo_name: testing
      target_branch: main
  locust:
    conda:
      relative_conda_path: miniconda3/bin/conda
      python_version: 3.13
      locust_version: 2.37.10
    image:
      neox-locust: 2025062701
      relative_dir_path_in_repo: Performance/Locust/env
    requirements:
      relative_dir_path_in_repo: Performance/Locust/env/requirements
    scenarios:
      relative_root_dir: docker-compose/locust
      container_name: locust
