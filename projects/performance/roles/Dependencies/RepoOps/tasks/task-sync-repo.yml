---
- name: Check latest repository status
  ansible.builtin.stat:
    path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
  register: repo_status

- name: Setup SSH keys for repository access
  block:
    - name: Ensure .ssh directory exists
      ansible.builtin.file:
        path: "{{ PERF.home_path }}/.ssh"
        state: directory
        mode: "0700"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"

    - name: Copy SSH keys
      ansible.builtin.copy:
        src: "{{ item[0] }}"
        dest: "{{ PERF.home_path }}/.ssh/{{ item[0] | basename }}"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "{{ item[1] }}"
        force: true
      loop:
        - - "roles/02-generator/files/keys/{{ PERF.repo.perf.ssh_key_name }}"
          - "0600"
        - - "roles/02-generator/files/keys/{{ PERF.repo.perf.ssh_key_name }}.pub"
          - "0644"
        - - "roles/02-generator/files/keys/config"
          - "0600"
      ignore_errors: true

- name: Ensure repository parent directory exists
  ansible.builtin.file:
    path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}"
    state: directory
    mode: "0755"
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"

- name: Clone repository if it does not exist
  ansible.builtin.git:
    repo: "{{ PERF.repo.perf.address }}"
    dest: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
    accept_hostkey: true
  environment:
    GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
  when: not repo_status.stat.exists
  ignore_errors: true

- name: Continue if repository exists or was just cloned
  when: repo_status.stat.exists or not repo_status.stat.exists
  block:
    - name: Check current branch
      ansible.builtin.command: "git branch --show-current"
      args:
        chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
      register: current_branch
      environment:
        GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
      changed_when: false
      failed_when: false

    - name: Switch to target branch if not already on it
      ansible.builtin.command: "git switch {{ PERF.repo.perf.target_branch | default('main') }}"
      args:
        chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
      environment:
        GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"
      when: current_branch.stdout != PERF.repo.perf.target_branch | default('main')

    - name: Pull latest changes
      ansible.builtin.git:
        repo: "{{ PERF.repo.perf.address }}"
        dest: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}"
        update: true # Equivalent to performing a git pull
        accept_hostkey: true
        force: true
      environment:
        GIT_SSH_COMMAND: "ssh -i {{ PERF.home_path }}/.ssh/{{ PERF.repo.perf.ssh_key_name }}"

    - name: Ensure repository directory permissions and ownership
      ansible.builtin.file:
        path: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}"
        state: directory
        mode: "0755"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        recurse: true
