# Performance Testing Framework

This Ansible framework provides automated deployment and management of performance testing infrastructure using Locust for load testing scenarios.

## Features

- **Automated Infrastructure Deployment**: Complete setup of performance testing environment
- **Locust Integration**: Built-in support for Locust load testing framework
- **Flexible Configuration**: Support for multiple testing scenarios via extra-vars
- **Scalable Architecture**: Support for distributed testing with generator nodes
- **Environment Isolation**: Separate preparation and deployment phases
- **Conda Environment Management**: Automated Python environment setup for testing tools

## Directory Structure

```
projects/performance/
├── ansible.cfg                              # Ansible configuration
├── site.yml                                 # Main playbook
├── README.md                                # This documentation
├── vault_pass.txt                          # Vault password file
├── inventory/                               # Inventory files for target hosts
├── keys/                                    # SSH keys for authentication
├── extra-vars/                             # External variable configurations
│   └── locust/                             # Locust-specific configurations
│       ├── locust-standalone-enquete.yml   # Enquete scenario configuration
│       └── locust-standalone-pres.yml      # Presentation scenario configuration
└── roles/
    ├── Dependencies/                        # Shared dependency roles
    │   ├── SetFacts/                       # Global variable management
    │   ├── SshConnection/                  # SSH connection management
    │   └── RepoOps/                        # Repository operations
    ├── 01-prepare/                         # Infrastructure preparation
    ├── 02-generator/                       # Generator service deployment
    └── 03-locust/                          # Locust testing framework
```

## Prerequisites

1. Ansible installed on the control machine
2. SSH access configured to target generator servers
3. Inventory file configured with generator groups
4. SSH keys set up for authentication
5. Target servers with sufficient resources for load testing

## Usage

### Infrastructure Deployment

Navigate to the performance directory before executing any commands:

```bash
cd projects/performance
```

#### Complete Deployment

Deploy all performance services (preparation + generator):

```bash
# All-in-one command: deploy all components
ansible-playbook site.yml -t deploy_all
```

#### Targeted Deployment

Deploy specific components individually:

```bash
# Infrastructure preparation only
ansible-playbook site.yml -t play_prepare

# Generator services only
ansible-playbook site.yml -t play_generator
```

### Locust Load Testing

#### Configuration Priority

Variable priority (highest to lowest):
1. Command-line specified variables (highest priority)
2. Extra-vars files (`extra-vars/locust/*.yml`)
3. Role default variables (`roles/03-locust/vars/main.yml`)

#### Standalone Mode Execution

Execute Locust in standalone mode with specific configuration:

```bash
# Using specific extra-vars configuration file
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"

# Alternative: Direct tag specification
ansible-playbook site.yml -t global,known_hosts,sync_repo,task_build_locust,standalone -e "@extra-vars/locust/locust-standalone-pres.yml"
```

#### Available Scenarios

- **Enquete Scenario**: `extra-vars/locust/locust-standalone-enquete.yml`
- **Presentation Scenario**: `extra-vars/locust/locust-standalone-pres.yml`

### Examples

```bash
# Deploy complete performance testing environment
ansible-playbook site.yml -t deploy_all

# Prepare infrastructure only
ansible-playbook site.yml -t play_prepare

# Deploy generator services
ansible-playbook site.yml -t play_generator

# Run Locust test with enquete scenario
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"

# Run Locust test with presentation scenario
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-pres.yml"
```

## Configuration

### Variables

Key configuration variables are defined in:
- `roles/Dependencies/SetFacts/vars/main.yml`: Global performance testing variables
- `roles/03-locust/vars/main.yml`: Default Locust configuration
- `extra-vars/locust/*.yml`: Scenario-specific overrides

### Inventory Groups

Configure your inventory file with appropriate generator groups:

```ini
[generator]
generator1.performance.local
generator2.performance.local

[locust]
locust1.performance.local
locust2.performance.local
```

### Extra Variables Files

Create custom scenario configurations in `extra-vars/locust/`:

```yaml
# Example: custom-scenario.yml
locust:
  target_host: "https://your-target-server.com"
  users: 100
  spawn_rate: 10
  run_time: "5m"
```

## Roles Description

### 01-prepare
- System updates and upgrades
- Docker installation and dependencies
- Pip3 installation
- Basic infrastructure preparation

### 02-generator
- Generator service deployment
- Conda environment setup
- Key management
- Service configuration

### 03-locust
- Locust framework installation
- Test scenario configuration
- Load testing execution
- Results collection

## Load Testing Best Practices

1. **Resource Planning**: Ensure generator servers have sufficient CPU and memory
2. **Network Considerations**: Monitor network bandwidth during testing
3. **Baseline Testing**: Start with small loads and gradually increase
4. **Monitoring**: Monitor both generator and target system metrics
5. **Test Duration**: Plan appropriate test durations for meaningful results
6. **Environment Isolation**: Use dedicated testing environments

## Monitoring and Logging

### Test Execution Logs

- Ansible execution logs: `./ansible.log`
- Locust test results: Check generator servers for detailed results
- System metrics: Monitor CPU, memory, and network usage

### Performance Metrics

Monitor the following during test execution:
- Response times
- Throughput (requests per second)
- Error rates
- Resource utilization on target systems

## Troubleshooting

### Common Issues

1. **Generator Connection Issues**: Verify SSH keys and inventory configuration
2. **Conda Environment Errors**: Check Python version compatibility
3. **Locust Startup Failures**: Verify target host accessibility
4. **Resource Constraints**: Monitor system resources on generator nodes

### Debug Mode

Run with verbose output for troubleshooting:

```bash
ansible-playbook site.yml -t deploy_all -vvv
```

### Validation Commands

```bash
# Check generator status
ansible generator -m ping

# Verify Locust installation
ansible generator -m shell -a "locust --version"

# Check conda environment
ansible generator -m shell -a "conda info --envs"
```

## Security Considerations

- Secure SSH keys and limit access to generator servers
- Use isolated networks for performance testing
- Avoid testing production systems without proper authorization
- Monitor and log all testing activities
- Implement proper cleanup procedures after testing

## Scaling Performance Tests

### Distributed Testing

For large-scale testing:
1. Deploy multiple generator nodes
2. Configure Locust in distributed mode
3. Use load balancers for result aggregation
4. Monitor resource usage across all nodes

### Resource Optimization

- Optimize test scripts for efficiency
- Use appropriate spawn rates
- Monitor and tune JVM/Python settings
- Consider using dedicated testing hardware

## Contributing

When adding new functionality:

1. Follow existing role structure and naming conventions
2. Add comprehensive documentation for new scenarios
3. Test thoroughly with different load patterns
4. Update configuration examples
5. Consider backward compatibility with existing scenarios

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Ansible and Locust documentation
3. Validate configuration files and inventory
4. Monitor system logs for detailed error information