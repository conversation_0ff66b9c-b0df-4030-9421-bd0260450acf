# Medical Backend Script Execution Framework

This Ansible framework provides a robust and idempotent way to execute shell scripts on remote servers in the medical backend infrastructure.

## Features

- **Robust Script Execution**: Includes error handling, timeouts, and proper exit code management
- **Idempotent Operations**: Safe to run multiple times with consistent results
- **Comprehensive Logging**: Detailed execution logs with timestamps and status tracking
- **Health Monitoring**: Built-in health checks and resource monitoring
- **Flexible Targeting**: Execute scripts on specific inventory groups
- **Backup Management**: Automatic backup and cleanup of old execution logs

## Directory Structure

```
projects/medical-backend/
├── ansible.cfg                              # Ansible configuration
├── site.yml                                 # Main playbook
├── README.md                                # This documentation
└── roles/
    ├── Dependencies/                        # Shared dependency roles
    │   ├── SetFacts/                        # Global variable management
    │   │   ├── tasks/main.yml
    │   │   └── vars/main.yml
    │   ├── SshConnection/                   # SSH connection management
    │   │   └── tasks/
    │   │       ├── main.yml
    │   │       └── task-known_hosts.yml
    │   └── RepoOps/                         # Repository operations
    │       └── tasks/
    │           ├── main.yml
    │           ├── task-git-config.yml
    │           └── task-sync-repo.yml
    ├── 01-common/                           # Main script execution role
    │   ├── meta/main.yml                    # Role dependencies
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   └── task-execute-shell-script.yml
    │   └── files/                           # Place your shell scripts here
    │       ├── *.sh                         # Your shell scripts
    │       └── health_check.sh              # Example health check script
    └── */                                   # Other roles
        └── ...                             
```

## Prerequisites

1. Ansible installed on the control machine
2. SSH access configured to target servers
3. Inventory file configured with target server groups
4. SSH keys set up for authentication

## Usage

### Basic Script Execution

To execute a script on a specific group of servers:

```bash
ansible-playbook site.yml -e "script_name=your_script.sh target_group=your_group"
```

### Examples

```bash
# Execute health check on all servers
ansible-playbook site.yml -e "target_group=all script_name=health_check.sh release_tag=0 remote_scripts_dir=/mnt/efs/production/devops/deploy"

# Execute deployment script with CLI command on backend servers
ansible-playbook site.yml -e "target_group=medical_servers script_name=backend.sh release_tag=v1.0.0 remote_scripts_dir=/mnt/efs/production/devops/deploy/bureau"

# Execute deployment script with extra-vars file on backend servers
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml"

# Execute with custom timeout (default is 300 seconds)
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml" --extra-vars "medical.scripts.execution_timeout=600"
```

### Display Usage Instructions

```bash
ansible-playbook site.yml --tags usage
```

## Adding New Scripts

1. Place your shell script in `roles/01-common/files/`
2. Ensure the script is executable and follows best practices:
   - Include `#!/bin/bash` shebang
   - Use `set -euo pipefail` for error handling
   - Provide clear output and error messages
   - Exit with appropriate codes (0 for success, non-zero for failure)

Example script structure:

```bash
#!/bin/bash
set -euo pipefail

echo "Starting script execution..."

# Your script logic here

if [ $? -eq 0 ]; then
    echo "Script completed successfully"
    exit 0
else
    echo "Script failed"
    exit 1
fi
```

## Configuration

### Variables

Key configuration variables are defined in `roles/Dependencies/SetFacts/vars/main.yml`:

- `medical.scripts.execution_timeout`: Script execution timeout (default: 300 seconds)
- `medical.scripts.relative_local_scripts_dir`: Directory to store shell scripts (relative to medical.repo.ansible.repo_root_dir)

### Inventory Groups

Configure your inventory file with appropriate groups:

```ini
[production_servers]
bureau.prod
medical.prod

[testig_servers]
bureau.testing
medical.testing
```

Or using yaml file:

```yaml
all:
  children:
    production:
      children:
        production_servers:
          hosts:
            bureau.prod:
              ansible_host: ***********
              ansible_user: ubuntu
            medical.prod:
              ansible_host: ***********
              ansible_user: ubuntu
    testing:
      children:
        testing_servers:
          hosts:
            bureau.testing:
              ansible_host: ***********
              ansible_user: ubuntu
            medical.testing:
              ansible_host: ***********
              ansible_user: ubuntu
```

## Logging and Monitoring

### Execution Logs

- Each script execution creates a detailed log file in `{remote_scripts_dir}/`
- Log files are named: `{script_name}_{timestamp}.log`
- A master execution log tracks all script runs: `execution.log`

### Log Cleanup

- Old log files are automatically cleaned up (keeps last 10 executions per script)
- Manual cleanup can be performed by running the cleanup tasks

### Monitoring Script Execution

Check execution status:

```bash
# View recent executions
ansible all -m shell -a "tail -20 {remote_scripts_dir}/execution.log"

# Check specific script logs
ansible all -m shell -a "ls -la {remote_scripts_dir} | grep your_script"
```

## Error Handling

The framework includes comprehensive error handling:

1. **Pre-execution validation**: Checks if script exists and variables are defined
2. **Timeout protection**: Scripts are terminated if they exceed the timeout
3. **Exit code monitoring**: Script failures are properly detected and reported
4. **Detailed error reporting**: Clear error messages with troubleshooting information
5. **Log preservation**: All execution details are preserved for debugging

## Best Practices

1. **Test scripts locally** before deploying through Ansible
2. **Use meaningful script names** that describe their purpose
3. **Include proper error handling** in your scripts
4. **Monitor execution logs** regularly for issues
5. **Keep scripts idempotent** - safe to run multiple times
6. **Use appropriate timeouts** for long-running scripts
7. **Document script purposes** and expected outcomes

## Troubleshooting

### Common Issues

1. **Script not found**: Ensure the script exists in `roles/01-common/files/`
2. **Permission denied**: Check script permissions and SSH access
3. **Timeout errors**: Increase timeout value or optimize script performance
4. **SSH connection issues**: Verify inventory and SSH key configuration

### Debug Mode

Run with verbose output for troubleshooting:

```bash
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml" -vvv
```

## Security Considerations

- Scripts are executed with appropriate permissions
- SSH keys should be properly secured
- Execution logs may contain sensitive information - secure log directories
- Review scripts for security implications before deployment
- Use sudo/become only when necessary

## Contributing

When adding new functionality:

1. Follow the existing code structure and naming conventions
2. Add appropriate error handling and logging
3. Update documentation and examples
4. Test thoroughly in a development environment
5. Consider backward compatibility
