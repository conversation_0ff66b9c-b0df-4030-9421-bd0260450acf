---
- name: Execute Medical Backend Bureau Scripts
  hosts: "{{ target_group | default('none') }}"
  gather_facts: true
  ignore_errors: false
  module_defaults:
    ansible.builtin.gather_facts:
      gather_timeout: 30
    ansible.builtin.setup:
      gather_timeout: 30
  roles:
    - Dep.SetFacts
    - Dep.SshConnection
    - 01-common
  tags:
    - medical_backend
    - execute_shell

- name: Upgrade Monitoring Services and Update Configs
  hosts: grafana_servers
  gather_facts: true
  ignore_errors: false
  module_defaults:
    ansible.builtin.gather_facts:
      gather_timeout: 30
    ansible.builtin.setup:
      gather_timeout: 30
  roles:
    - Dep.SetFacts
    - Dep.SshConnection
    - 02-grafana
  tags:
    - medical_grafana
    - monitor

- name: MongoDB Authentication Update
  hosts: localhost
  gather_facts: true
  ignore_errors: false
  connection: local
  module_defaults:
    ansible.builtin.gather_facts:
      gather_timeout: 30
    ansible.builtin.setup:
      gather_timeout: 30
  roles:
    - Dep.SetFacts
    - Dep.SshConnection
    - 03-database
  tags:
    - medical_database
    - mongo_auth_update
    - database

- name: Prepare target hosts for service restart
  hosts: localhost
  gather_facts: false
  roles:
    - Dep.SetFacts
    - 04-restart-services
  tags:
    - restart_services

- name: Execute dependencies for service restart
  hosts: restart_targets
  gather_facts: true
  ignore_errors: false
  module_defaults:
    ansible.builtin.gather_facts:
      gather_timeout: 30
    ansible.builtin.setup:
      gather_timeout: 30
  roles:
    - Dep.SetFacts
    - Dep.SshConnection
  tags:
    - restart_services

- name: Restart Medical Backend Services
  hosts: restart_targets
  gather_facts: false
  ignore_errors: false
  vars:
    restart_groups: "{{ hostvars['localhost']['restart_groups'] | default([]) }}"
    restart_services: "{{ hostvars['localhost']['restart_services'] | default([]) }}"
  roles:
    - 04-restart-services
  tags:
    - restart_services

# Example playbook for specific script execution
- name: Example - Execute specific script on target group
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Display script execution usage instructions
      ansible.builtin.debug:
        msg: |
          Medical Backend Script Execution Usage:
          =====================================
          Required variables:
          - script_name: Name of the script file in roles/01-common/files/
          - target_group: Inventory group name to execute on
          - release_tag: Release tag for the script
          - remote_scripts_dir: Remote directory to copy and execute scripts
          - sync_base_shells: Whether to sync base shells to remote hosts (default: true)

          Optional variables:
          - execution_timeout: Script timeout in seconds (default: -1)

          Examples:
          ansible-playbook site.yml -t execute_shell -e "@extra-vars/kenta.backend.yml"
          ansible-playbook site.yml -t execute_shell -e "@extra-vars/bureau.backend.yml" \
            --extra-vars "MEDICAL.scripts.execution_timeout=600"
          ansible-playbook site.yml -t execute_shell \
            -e "target_group=your_group script_name=your_script.sh release_tag=release_tag remote_scripts_dir=/your/dir/path"
  tags:
    - never
    - script_execution_usage

# Example playbook for Grafana monitoring services
- name: Example - Upgrade monitoring services and update configs
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Display Grafana usage instructions
      ansible.builtin.debug:
        msg: |
          Grafana Monitoring Services Usage:
          =====================================
          Required variables:
          - grafana_image_name: grafana/grafana
          - grafana_version: Grafana version
          - loki_image_name: grafana/loki
          - loki_version: Loki version
          - promtail_image_name: grafana/promtail
          - promtail_version: Promtail version
          - prometheus_image_name: prom/prometheus
          - prometheus_version: Prometheus version
          - node_exporter_image_name: prom/node-exporter
          - node_exporter_version: Node exporter version
          - sync_docker_compose: Whether to sync docker-compose.yml to remote hosts (default: true)
          - relative_prometheus_config_dir: prometheus/config (sub path after root path with docker-compose.yml)
          - relative_redis_monitor_config_dir: service/redis/conf (sub path after root path with docker-compose.yml)
          - relative_loki_config_dir: loki/config (sub path after root path with docker-compose.yml)

          Examples:
          ansible-playbook site.yml -t medical_grafana -e "@extra-vars/monitor.yml"
          ansible-playbook site.yml -t upgrade_services -e "@extra-vars/monitor.yml"
          ansible-playbook site.yml -t update_configs -e "@extra-vars/monitor.yml"
  tags:
    - never
    - grafana_usage

# Example playbook for MongoDB authentication update
- name: Example - MongoDB authentication update
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Display MongoDB authentication update usage instructions
      ansible.builtin.debug:
        msg: |
          MongoDB Authentication Update Usage:
          ===================================
          Required variables:
          - old_string: The string to be replaced in configuration files
          - new_string: The new string to replace with

          The task will process all services defined in MEDICAL.database.services:
          - 贤太API集群 (***********)
          - 事务局 (***********)
          - 贤太识别端 (************)
          - Smart药局 (***********)
          - GreenMedic (**********, ***********)
          - selector环境 (***********)
          - neox-inc官网 (*************)
          - smart药局统计执行文件相关 (***********)

          Examples:
          ansible-playbook site.yml -t mongo_auth_update -e "@extra-vars/mongo.auth.update.yml"
          ansible-playbook site.yml -t mongo_auth_update -e "old_string='yakumaru:oldpassword' new_string='yakumaru:newpassword'"
          ansible-playbook site.yml -t medical_database -e "old_string='***********************' new_string='***********************'"
  tags:
    - never
    - mongodb_auth_update_usage

# Example playbook for service restart
- name: Example - Restart services by group or individual services
  hosts: localhost
  gather_facts: false
  roles:
    - role: 04-restart-services
  tasks:
    - name: Display service restart usage instructions
      ansible.builtin.debug:
        msg: |
          Medical Backend Service Restart Usage:
          =====================================
          This task provides robust and idempotent service restart functionality.
          Supports both group-based and individual service restart operations.

          Required variables (at least one must be specified):
          - restart_groups: List of service groups to restart
          - restart_services: List of individual services to restart

          Available Groups:
          - recognize (识别端): async-merge, async-dispatcher, med-queue, async-recognize
          - qps (QPS): selector
          - api (API集群): php-fpm-74, nginx, med-queue
          - bureau (事务局端): bureau-queue, med-cover-original, prescription
          - smart (Smart药局): smart-queue

          Available Individual Services:
          - async-merge, async-dispatcher, med-queue, async-recognize
          - selector, php-fpm-74, nginx, bureau-queue
          - med-cover-original, prescription, smart-queue

          Configuration Options (in roles/04-restart-services/vars/main.yml):
          - remote_logs_dir: Log directory (default: /mnt/efs/production/devops/logs/ansible-service-restart-logs)
          - execution_timeout: Timeout in seconds, -1 for no timeout (default: -1)
          - max_retry_attempts: Number of retry attempts (default: 3)
          - retry_delay: Delay between retries in seconds (default: 10)

          Examples:

          1. Restart services by group:
          ansible-playbook site.yml -t restart_services -e "restart_groups=['recognize']"
          ansible-playbook site.yml -t restart_services -e "restart_groups=['recognize','api']"

          2. Restart individual services:
          ansible-playbook site.yml -t restart_services -e "restart_services=['nginx','php-fpm-74']"
          ansible-playbook site.yml -t restart_services -e "restart_services=['async-merge']"

          3. Mixed restart (groups + individual services):
          ansible-playbook site.yml -t restart_services -e "restart_groups=['qps'] restart_services=['nginx','bureau-queue']"

          4. With custom configuration:
          ansible-playbook site.yml -t restart_services -e "restart_groups=['api']" --extra-vars "services_info.restart.defaults.execution_timeout=300"

          Features:
          - Robust error handling and retry mechanism
          - Pre and post-restart status verification
          - Comprehensive logging and status tracking
          - Idempotent operations (safe to run multiple times)
          - Partial failure support (continues with other services)
          - Automatic log cleanup (keeps last 20 executions)

          Log Files Location: /mnt/efs/production/devops/logs/ansible-service-restart-logs/
          - service-restart-execution.log: Main execution log
          - service-status-{service}-{timestamp}.log: Service status logs
          - service-restart-{service}-{timestamp}.log: Individual restart logs
  tags:
    - never
    - restart_services_usage
