---
# Medical Backend Infrastructure Playbook
# This playbook manages medical backend services including script execution,
# monitoring services, database operations, and service restarts

# Common module defaults for all plays
- name: Set common module defaults
  hosts: localhost
  gather_facts: false
  tasks:
    - name: Set global module defaults
      ansible.builtin.set_fact:
        common_module_defaults:
          ansible.builtin.gather_facts:
            gather_timeout: 30
          ansible.builtin.setup:
            gather_timeout: 30
  tags: always

- name: Execute Medical Backend Bureau Scripts
  hosts: "{{ target_group | default(omit) }}"
  gather_facts: true
  ignore_errors: false
  module_defaults: "{{ hostvars['localhost']['common_module_defaults'] | default({}) }}"
  pre_tasks:
    - name: Validate target group
      ansible.builtin.fail:
        msg: |
          Error: target_group is required for script execution.
          Please specify a valid target group from inventory.
          Available groups: {{ groups.keys() | list | join(', ') }}
      when: target_group is not defined or target_group == ""
  roles:
    - Dep.SetFacts
    - Dep.SshConnection
    - 01-common
  tags:
    - medical_backend
    - execute_shell

- name: Upgrade Monitoring Services and Update Configs
  hosts: grafana_servers
  gather_facts: true
  ignore_errors: false
  module_defaults: "{{ hostvars['localhost']['common_module_defaults'] | default({}) }}"
  roles:
    - Dep.SetFacts
    - Dep.SshConnection
    - 02-grafana
  tags:
    - medical_grafana
    - monitor

- name: MongoDB Authentication Update
  hosts: localhost
  gather_facts: true
  ignore_errors: false
  connection: local
  module_defaults: "{{ hostvars['localhost']['common_module_defaults'] | default({}) }}"
  roles:
    - Dep.SetFacts
    - Dep.SshConnection
    - 03-database
  tags:
    - medical_database
    - mongo_auth_update
    - database

- name: Prepare target hosts for service restart
  hosts: localhost
  gather_facts: false
  roles:
    - Dep.SetFacts
    - 04-restart-services
  tags:
    - restart_services

- name: Execute dependencies for service restart
  hosts: restart_targets
  gather_facts: true
  ignore_errors: false
  module_defaults: "{{ hostvars['localhost']['common_module_defaults'] | default({}) }}"
  roles:
    - Dep.SetFacts
    - Dep.SshConnection
  tags:
    - restart_services

- name: Restart Medical Backend Services
  hosts: restart_targets
  gather_facts: false
  ignore_errors: false
  vars:
    restart_groups: "{{ hostvars['localhost']['restart_groups'] | default([]) }}"
    restart_services: "{{ hostvars['localhost']['restart_services'] | default([]) }}"
  roles:
    - 04-restart-services
  tags:
    - restart_services


