---
# Process single file - handles string replacement with idempotency
# This file is included for each file. It expects 'current_file', 'current_ip', 'mongo_old_string', 'mongo_new_string' from parent.

- name: "File: {{ current_ip }}:{{ current_file }} | Check if file exists"
  stat:
    path: "{{ current_file }}"
  register: file_stat
  delegate_to: "{{ current_ip }}"
  failed_when: false

- name: "File: {{ current_ip }}:{{ current_file }} | Skip processing if file does not exist"
  ansible.builtin.debug:
    msg: "Warning: File {{ current_file }} does not exist on {{ current_ip }}. Skipping this file."
  when: not file_stat.stat.exists | default(false)

- name: "File: {{ current_ip }}:{{ current_file }} | Process file replacement"
  block:
    - name: "File: {{ current_ip }}:{{ current_file }} | Check if old string exists in file"
      shell: |
        if [ -f "{{ current_file }}" ]; then
          if grep -F "{{ mongo_old_string }}" "{{ current_file }}" >/dev/null 2>&1; then
            echo "FOUND"
          else
            echo "NOT_FOUND"
          fi
        else
          echo "FILE_NOT_EXISTS"
        fi
      register: string_check_result
      delegate_to: "{{ current_ip }}"
      changed_when: false
      failed_when: false

    - name: "File: {{ current_ip }}:{{ current_file }} | Create backup before replacement"
      copy:
        src: "{{ current_file }}"
        dest: "{{ current_file }}.backup.{{ ansible_date_time.epoch }}"
        remote_src: true
        backup: false
      delegate_to: "{{ current_ip }}"
      when: string_check_result.stdout == "FOUND"

    - name: "File: {{ current_ip }}:{{ current_file }} | Perform string replacement"
      replace:
        path: "{{ current_file }}"
        regexp: "{{ mongo_old_string | regex_escape }}"
        replace: "{{ mongo_new_string }}"
        backup: true
      register: replacement_result
      delegate_to: "{{ current_ip }}"
      when: string_check_result.stdout == "FOUND"

    - name: "File: {{ current_ip }}:{{ current_file }} | Verify replacement result"
      shell: |
        if [ -f "{{ current_file }}" ]; then
          if grep -F "{{ mongo_new_string }}" "{{ current_file }}" >/dev/null 2>&1; then
            echo "REPLACEMENT_SUCCESS"
          else
            echo "REPLACEMENT_FAILED"
          fi
        else
          echo "FILE_NOT_EXISTS"
        fi
      register: verification_result
      delegate_to: "{{ current_ip }}"
      changed_when: false
      failed_when: false
      when: string_check_result.stdout == "FOUND"

    - name: "File: {{ current_ip }}:{{ current_file }} | Display replacement result"
      ansible.builtin.debug:
        msg: |
          File Processing Result: {{ current_ip }}:{{ current_file }}
          =======================================================
          File exists: {{ file_stat.stat.exists | default(false) }}
          Old string found: {{ 'Yes' if string_check_result.stdout == 'FOUND' else 'No' }}
          {% if string_check_result.stdout == "FOUND" %}
          Replacement performed: {{ 'Yes' if replacement_result.changed else 'No' }}
          Verification status: {{ verification_result.stdout }}
          {% if replacement_result.changed %}
          Backup created: {{ replacement_result.backup_file | default('N/A') }}
          {% endif %}
          {% else %}
          Action taken: No replacement needed (old string not found)
          {% endif %}
          Old string: {{ mongo_old_string }}
          New string: {{ mongo_new_string }}

    - name: "File: {{ current_ip }}:{{ current_file }} | Report string not found"
      ansible.builtin.debug:
        msg: |
          No Replacement Needed: {{ current_ip }}:{{ current_file }}
          ================================================
          Reason: Old string "{{ mongo_old_string }}" was not found in the file.
          This is normal if the replacement was already performed previously.
          File content check status: {{ string_check_result.stdout }}
      when: string_check_result.stdout != "FOUND"

    - name: "File: {{ current_ip }}:{{ current_file }} | Handle replacement failure"
      ansible.builtin.debug:
        msg: |
          WARNING: Replacement verification failed for {{ current_ip }}:{{ current_file }}
          This might indicate a partial replacement or file corruption.
          Please verify the file content manually.
          Expected new string: {{ mongo_new_string }}
          Verification result: {{ verification_result.stdout }}
      when:
        - string_check_result.stdout == "FOUND"
        - verification_result.stdout is defined
        - verification_result.stdout != "REPLACEMENT_SUCCESS"

  when: file_stat.stat.exists | default(false)
