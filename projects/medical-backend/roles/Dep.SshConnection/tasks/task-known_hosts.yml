---
- name: Check if known_hosts file exists on localhost
  stat:
    path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/.ssh/known_hosts"
  register: known_hosts_stat
  delegate_to: localhost

- name: Create known_hosts file if it does not exist on localhost
  file:
    path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/.ssh/known_hosts"
    state: touch
    mode: "0600"
  when: not known_hosts_stat.stat.exists
  delegate_to: localhost

- name: Set target hosts list (handle both hostnames and groups)
  set_fact:
    target_hosts_resolved: "{{ query('inventory_hostnames', target_group) }}"
  when: target_group is defined

- name: Debug resolved target hosts
  ansible.builtin.debug:
    var: target_hosts_resolved
  when: target_hosts_resolved is defined

- name: Check if host key exists in known_hosts
  shell: "ssh-keygen -F {{ item }}" # item will be the IP address from inventory
  register: host_key_check
  failed_when: false
  changed_when: false
  loop: "{{ target_hosts_resolved | default([]) }}"
  delegate_to: localhost
  when:
    - target_group is defined
    - target_hosts_resolved is defined
    - target_hosts_resolved | length > 0

- name: Add host key to known_hosts if not present
  known_hosts:
    name: "{{ item.item }}" # Use the IP address from inventory as the name in known_hosts
    key: "{{ lookup('pipe', 'ssh-keyscan -T 10 -H ' + item.item) }}" # Scan the IP address
    path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/.ssh/known_hosts"
    state: present
  when:
    - target_group is defined
    - target_hosts_resolved is defined
    - target_hosts_resolved | length > 0
    - item.rc != 0 # Only add if ssh-keygen -F reported host not found
  loop: "{{ host_key_check.results | default([]) }}"
  delegate_to: localhost
