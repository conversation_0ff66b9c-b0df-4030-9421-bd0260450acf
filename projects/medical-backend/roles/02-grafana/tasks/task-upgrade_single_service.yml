---
# This file is included for each service. It expects 'service_name' from the loop.
- name: "Check Service | Get current image for '{{ service_name }}' container"
  command: "docker inspect --format='{{ '{{' }}.Config.Image{{ '}}' }}' {{ service_name }}"
  register: current_image_raw
  changed_when: false
  failed_when: false
  become: false

- name: "Check Service | Determine if '{{ service_name }}' needs an upgrade"
  vars:
    expected_image: "{{ vars[service_name.replace('-', '_') ~ '_image_name'] }}:{{ vars[service_name.replace('-', '_') ~ '_version'] }}"
  set_fact:
    is_upgrade_needed: "{{ current_image_raw.rc != 0 or current_image_raw.stdout != expected_image }}"
    current_image: "{{ current_image_raw.stdout | default('Not running or does not exist') }}"

- name: "Execute Upgrade | Update '{{ service_name }}' (if needed)"
  shell: "docker-compose up -d --force-recreate {{ service_name }}"
  args:
    chdir: "{{ MEDICAL.monitor.remote_root_path }}"
  register: compose_up_result
  when: is_upgrade_needed
  become: false

- name: "Upgrade Report | '{{ service_name }}'"
  vars:
    expected_image: "{{ vars[service_name.replace('-', '_') ~ '_image_name'] }}:{{ vars[service_name.replace('-', '_') ~ '_version'] }}"
  debug:
    msg: |
      Service '{{ service_name }}' upgrade complete.
      Reason: {% if current_image_raw.rc != 0 %}Container does not exist or is not running{% else %}Image outdated (Current: '{{ current_image }}', Target: '{{ expected_image }}'){% endif %}.
      Docker Compose output:
      {{ compose_up_result.stdout | indent(2) }}
  when: is_upgrade_needed and compose_up_result is changed

- name: "Status Report | '{{ service_name }}' is up to date"
  vars:
    expected_image: "{{ vars[service_name.replace('-', '_') ~ '_image_name'] }}:{{ vars[service_name.replace('-', '_') ~ '_version'] }}"
  debug:
    msg: "Service '{{ service_name }}' is already up to date (Image: '{{ expected_image }}'). No action needed."
  when: not is_upgrade_needed
