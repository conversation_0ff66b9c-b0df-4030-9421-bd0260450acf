---
# Medical Backend Service Restart Role
# This role handles service restart operations for the medical backend infrastructure
# Supports both group-based and individual service restart operations

- name: Validate input parameters
  ansible.builtin.fail:
    msg: |
      Invalid input parameters:
      - At least one of 'restart_groups' or 'restart_services' must be specified
      - restart_groups: {{ restart_groups | default([]) }}
      - restart_services: {{ restart_services | default([]) }}
  when:
    - (restart_groups | default([]) | length == 0)
    - (restart_services | default([]) | length == 0)

- name: Display service restart plan overview
  ansible.builtin.debug:
    msg: |
      Service Restart Plan Overview:
      =============================
      Groups to restart: {{ restart_groups | default([]) | join(', ') if restart_groups | default([]) | length > 0 else 'None' }}
      Individual services: {{ restart_services | default([]) | join(', ') if restart_services | default([]) | length > 0 else 'None' }}

      Detailed planning and execution will be handled in the main restart task.

- name: Import service restart tasks
  ansible.builtin.import_tasks: task-restart-services.yml
