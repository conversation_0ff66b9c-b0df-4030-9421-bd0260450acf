---
# Medical Backend Global Variables
# This file contains common configurations used across all medical backend playbooks
# to eliminate duplication and ensure consistency

# Common timeout settings for medical backend operations
common_timeouts:
  fact_gathering: 30
  ssh_connection: 90
  script_execution: -1 # No timeout by default

# Common backup settings
common_backup_config:
  enabled: true
  create_backup: true

# Common file permissions for medical backend
common_file_permissions:
  script_mode: "0755"
  config_mode: "0644"
  log_mode: "0644"
  directory_mode: "0755"

# Common retry settings for operations
common_retry_config:
  max_attempts: 3
  delay_seconds: 10

# Common logging configuration
common_logging:
  enabled: true
  format: '{{ ansible_date_time.iso8601 | default(lookup(''pipe'', ''date -u +"%Y-%m-%dT%H:%M:%SZ"'')) }}'
  cleanup_old_logs: true
  keep_log_count: 20
