# Terraform for Infrastructure Management

This document provides instructions and guidelines for using Terraform to manage infrastructure resources for this project.

## Overview

The Terraform configurations in this directory are designed to manage specific cloud resources in a modular and reusable way. Currently, the primary use case is to control the state (start/stop) of existing AWS EC2 instances.

## Prerequisites

Before you can use these Terraform configurations, you need to have the following tools installed and configured on your local machine.

### 1. Terraform Installation

Terraform is required to execute the configuration files.

- **Official Documentation**: You can find detailed installation instructions for all operating systems on the [official Terraform website](https://learn.hashicorp.com/tutorials/terraform/install-cli).

- **Example (macOS with Homebrew)**:
  ```sh
  brew tap hashicorp/tap
  brew install hashicorp/tap/terraform
  ```

### 2. AWS CLI Installation

The `local-exec` provisioner in the configuration files relies on the AWS Command Line Interface (CLI) to execute commands against the AWS API.

- **Official Documentation**: Please follow the instructions in the [AWS CLI User Guide](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-install.html) to install it.

## Directory Structure

The `terraform/` directory is organized to separate configurations by project or environment.

```
terraform/
└── kenta/
    └── windows/
        └── main.tf
```

- **`kenta/`**: Represents a specific project, environment, or context (e.g., "Kenta Project").
- **`windows/`**: Contains Terraform code specifically for managing Windows-related resources within the "kenta" context.
- **`main.tf`**: The core Terraform file for this module. It is configured to start or stop a pre-existing EC2 instance, not to create a new one. It uses a `null_resource` to trigger an AWS CLI command based on input variables.

## AWS Credentials Configuration

To allow Terraform to interact with your AWS account, you must configure your AWS credentials. **Never hardcode credentials directly in `.tf` files.** Here are the recommended methods, in order of preference:

### Method 1: Environment Variables (Recommended for Local Development)

Terraform automatically detects and uses these standard environment variables. This is a secure and straightforward method for local use.

In your terminal, export the following variables:

```sh
export AWS_ACCESS_KEY_ID="YOUR_AWS_ACCESS_KEY"
export AWS_SECRET_ACCESS_KEY="YOUR_AWS_SECRET_KEY"

# Optional: Set a default region
export AWS_DEFAULT_REGION="us-east-1"
```

**Note**: These variables are only set for the current terminal session. To make them permanent, add them to your shell's profile script (e.g., `~/.zshrc`, `~/.bash_profile`).

### Method 2: Shared Credentials File (Recommended for Local Development)

The AWS CLI and Terraform can share a credentials file. This is ideal for managing multiple AWS profiles.

1.  **Location**:
    -   **Linux/macOS**: `~/.aws/credentials`
    -   **Windows**: `C:\Users\<USER>\.aws\credentials`

2.  **Format**: The file should be in INI format. For the default profile, it looks like this:

    ```ini
    [default]
    aws_access_key_id = YOUR_AWS_ACCESS_KEY
    aws_secret_access_key = YOUR_AWS_SECRET_KEY
    ```

Terraform will automatically read credentials from the `[default]` profile.

### Method 3: IAM Roles (Best Practice for AWS Environments)

When running Terraform from an AWS EC2 instance (e.g., in a CI/CD pipeline), using an IAM role is the most secure method.

1.  Create an IAM role with the necessary permissions.
2.  Attach the role to the EC2 instance where Terraform will run.

Terraform will automatically acquire temporary credentials from the instance's metadata service, eliminating the need to store any long-term keys.

## Usage

To run the instance control script:

1.  **Navigate to the directory**:
    ```sh
    cd terraform/kenta/windows
    ```

2.  **Initialize Terraform**:
    This command downloads the required provider plugins. You only need to run this once per project.
    ```sh
    terraform init
    ```

3.  **Apply the configuration**:
    Use the `terraform apply` command with variables to specify the target instance and the desired action.

    - **To start the instance**:
      ```sh
      terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=start"
      ```

    - **To stop the instance**:
      ```sh
      terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=stop"
      ```

    Replace the `region` and `instance_id` with your actual values. Terraform will prompt for confirmation before proceeding.

### Method 2: Using a `.tfvars` file (Recommended)

For a more organized approach, you can define your variables in a `.tfvars` file. This avoids passing variables on the command line.

1.  **Create a variables file**:
    In the `terraform/kenta/windows/` directory, create a file named `terraform.tfvars`. You can copy the provided example file:
    ```sh
    cp terraform.tfvars.example terraform.tfvars
    ```

2.  **Edit `terraform.tfvars`**:
    Open the `terraform.tfvars` file and set the values for your specific instance and desired action.
    ```hcl
    region      = "us-east-1"
    instance_id = "i-your_actual_instance_id"
    action      = "start"
    ```

3.  **Apply the configuration**:
    Now you can run `apply` without any command-line variables. Terraform automatically loads `terraform.tfvars` if it exists.
    ```sh
    terraform apply
    ```
    Terraform will review the plan and prompt for confirmation. 